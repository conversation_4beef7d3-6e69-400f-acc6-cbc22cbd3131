#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

# Načteme data ze souboru 14.xlsx
def nacti_data_ze_souboru(filename):
    df = pd.read_excel(filename)
    df_clean = df[['oznaceni', 'prumer', 'delka (m)', 'ks', 'celkem delka (m)']].dropna()
    
    segments = []
    prumer = df_clean['prumer'].iloc[0]
    
    for _, row in df_clean.iterrows():
        delka_cm = int(row['delka (m)'] * 100)
        segments.append({
            'oznaceni': row['oznaceni'],
            'prumer': prumer,
            'delka': delka_cm,
            'ks': row['ks'],
            'celkem_delka': delka_cm * row['ks']
        })
    
    return segments, prumer

segments, prumer = nacti_data_ze_souboru('14.xlsx')

print('=== DEBUG ANALÝZA ===')
celkova_delka_check = 0
celkovy_pocet_check = 0
for seg in segments:
    print(f"{seg['oznaceni']}: {seg['delka']}cm x {seg['ks']} = {seg['celkem_delka']}cm")
    celkova_delka_check += seg['celkem_delka']
    celkovy_pocet_check += seg['ks']

print(f'Celková délka ze segments: {celkova_delka_check}cm')
print(f'Celkový počet segmentů: {celkovy_pocet_check}')

# Expandujeme segmenty
vsechny_segmenty = []
for seg in segments:
    for _ in range(seg['ks']):
        vsechny_segmenty.append(seg.copy())

print(f'Po expandování: {len(vsechny_segmenty)} segmentů')
celkova_delka_expandovane = sum(s['delka'] for s in vsechny_segmenty)
print(f'Celková délka expandovaných: {celkova_delka_expandovane}cm')

# Kontrola
print(f'Rozdíl: {celkova_delka_check - celkova_delka_expandovane}cm')

# Simulujeme optimalizaci
PROREZ_NA_REZ = 0.4  # cm
delka_tyci = 600  # cm

# Seřadíme podle délky sestupně
vsechny_segmenty.sort(key=lambda x: x['delka'], reverse=True)

tyci = []  # Seznam tyčí, každá tyč je seznam segmentů

for i, segment in enumerate(vsechny_segmenty):
    umisten = False

    # Zkusíme umístit segment do existující tyče
    for tyc in tyci:
        # Vypočítáme aktuální využití tyče
        aktualni_delka = sum(s['delka'] for s in tyc)
        # Prorez: jeden řez na konci každého segmentu + jeden řez pro nový segment
        aktualni_prorez = (len(tyc) + 1) * PROREZ_NA_REZ
        zbyvajici_misto = delka_tyci - aktualni_delka - aktualni_prorez

        if segment['delka'] <= zbyvajici_misto:
            tyc.append(segment)
            umisten = True
            break

    # Pokud se segment nevešel do žádné existující tyče, vytvoříme novou
    if not umisten:
        tyci.append([segment])

print(f'\nVýsledek optimalizace:')
print(f'Počet tyčí: {len(tyci)}')

# Spočítáme celkovou délku umístěných segmentů
celkova_delka_umistene = 0
celkovy_pocet_umistene = 0
for tyc in tyci:
    for segment in tyc:
        celkova_delka_umistene += segment['delka']
        celkovy_pocet_umistene += 1

print(f'Celková délka umístěných segmentů: {celkova_delka_umistene}cm')
print(f'Celkový počet umístěných segmentů: {celkovy_pocet_umistene}')
print(f'Chybí: {len(vsechny_segmenty) - celkovy_pocet_umistene} segmentů')
print(f'Chybí délka: {celkova_delka_expandovane - celkova_delka_umistene}cm')

# Spočítáme odpad a efektivitu
celkovy_odpad = 0
for tyc in tyci:
    celkova_delka_tyci = sum(s['delka'] for s in tyc)
    celkovy_prorez_tyci = len(tyc) * PROREZ_NA_REZ
    odpad = delka_tyci - celkova_delka_tyci - celkovy_prorez_tyci
    celkovy_odpad += odpad

pocet_tyci = len(tyci)
celkovy_material = pocet_tyci * delka_tyci
efektivita = (celkova_delka_umistene / celkovy_material) * 100 if celkovy_material > 0 else 0

print(f'\nKontrola výpočtů:')
print(f'Celkový materiál: {pocet_tyci} x {delka_tyci}cm = {celkovy_material}cm')
print(f'Využitá délka: {celkova_delka_umistene}cm')
print(f'Efektivita: {efektivita:.1f}%')
print(f'Celkový odpad: {celkovy_odpad:.1f}cm')

# Kontrola: materiál = využitá délka + prorez + odpad
celkovy_prorez = sum(len(tyc) * PROREZ_NA_REZ for tyc in tyci)
kontrola = celkova_delka_umistene + celkovy_prorez + celkovy_odpad
print(f'Kontrola: {celkova_delka_umistene} + {celkovy_prorez} + {celkovy_odpad:.1f} = {kontrola:.1f}cm')
print(f'Materiál: {celkovy_material}cm')
print(f'Rozdíl: {celkovy_material - kontrola:.1f}cm')

# Analýza délek segmentů
print(f'\nAnalýza délek segmentů:')
delky = [s['delka'] for s in vsechny_segmenty]
delky.sort(reverse=True)
print(f'Nejdelší segmenty: {delky[:10]}')
print(f'Nejkratší segmenty: {delky[-10:]}')

# Kolik segmentů je delších než 600cm?
dlouhe_segmenty = [d for d in delky if d > 600]
print(f'Segmenty delší než 600cm: {len(dlouhe_segmenty)}')
if dlouhe_segmenty:
    print(f'Délky: {dlouhe_segmenty}')

# Kolik segmentů se nevejde do 6m tyče s prořezem?
max_delka_s_prořezem = 600 - PROREZ_NA_REZ
prilis_dlouhe = [d for d in delky if d > max_delka_s_prořezem]
print(f'Segmenty delší než {max_delka_s_prořezem}cm: {len(prilis_dlouhe)}')

# Analýza tyčí
print(f'\nAnalýza tyčí:')
pocty_segmentu = [len(tyc) for tyc in tyci]
print(f'Průměrný počet segmentů na tyč: {sum(pocty_segmentu)/len(pocty_segmentu):.2f}')
print(f'Tyče s 1 segmentem: {pocty_segmentu.count(1)}')
print(f'Tyče s 2 segmenty: {pocty_segmentu.count(2)}')
print(f'Tyče s 3+ segmenty: {len([p for p in pocty_segmentu if p >= 3])}')
