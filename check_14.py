#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

df = pd.read_excel('14.xlsx')
df_clean = df[['oznaceni', 'prumer', 'delka (m)', 'ks', 'celkem delka (m)']].dropna()

print('Segmenty v souboru 14.xlsx:')
for _, row in df_clean.iterrows():
    delka_m = row['delka (m)']
    print(f"{row['oznaceni']}: {delka_m:.2f}m ({delka_m*100:.0f}cm) x {row['ks']} ks")
    if delka_m > 6:
        print(f'  *** DELŠÍ NEŽ 6m! ***')

print('\nSegmenty delší než 6m:')
dlouhe = df_clean[df_clean['delka (m)'] > 6]
if len(dlouhe) > 0:
    for _, row in dlouhe.iterrows():
        print(f"{row['oznaceni']}: {row['delka (m)']:.2f}m x {row['ks']} ks")
else:
    print('Žádné segmenty delší než 6m')
