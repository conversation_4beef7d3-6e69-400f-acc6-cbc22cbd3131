#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optimalizace řezání výztuže - minimalizace odpadu
Řeší problém 1D bin packing pro řezání výztuže z 6m tyčí
"""

import pandas as pd
import numpy as np
from itertools import combinations_with_replacement, product
import openpyxl
from openpyxl.styles import PatternFill, Font, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import random
from collections import defaultdict

# Konstanty
TYCA_DELKA = 600  # cm (6m)
PROREZ_NA_REZ = 0.4  # cm (4mm)

def nacti_data():
    """Načte data z Excel souboru"""
    df = pd.read_excel('H:/Makra/Test/6.xlsx')
    
    # Převod na cm a příprava dat
    segments = []
    for _, row in df.iterrows():
        delka_cm = int(row['delka (m)'] * 100)  # převod na cm
        segments.append({
            'oznaceni': row['oznaceni'],
            'prumer': row['prumer'],
            'delka': delka_cm,
            'ks': row['ks'],
            'celkem_delka': delka_cm * row['ks']
        })
    
    return segments

def generuj_kombinace_optimalizovane(segments):
    """
    Generuje optimální kombinace segmentů pro jednu tyč
    Používá dynamické programování a heuristiky
    """
    # Vytvoříme seznam unikátních délek
    unikatni_delky = {}
    for seg in segments:
        delka = seg['delka']
        if delka not in unikatni_delky:
            unikatni_delky[delka] = []
        unikatni_delky[delka].append(seg)

    kombinace = []

    # Generujeme kombinace pomocí dynamického programování
    def najdi_nejlepsi_kombinace(zbyvajici_delka, dostupne_typy, memo={}):
        if zbyvajici_delka <= 0:
            return []

        key = (zbyvajici_delka, tuple(sorted(dostupne_typy.keys())))
        if key in memo:
            return memo[key]

        nejlepsi = []
        nejlepsi_hodnota = 0

        for delka, segmenty in dostupne_typy.items():
            potrebna_delka = delka + PROREZ_NA_REZ
            if potrebna_delka <= zbyvajici_delka:
                # Zkusíme přidat tento segment
                novy_dostupne = dostupne_typy.copy()
                if len(segmenty) > 1:
                    novy_dostupne[delka] = segmenty[1:]
                else:
                    del novy_dostupne[delka]

                zbytek = najdi_nejlepsi_kombinace(zbyvajici_delka - potrebna_delka, novy_dostupne, memo)
                kombinace_s_timto = [segmenty[0]] + zbytek

                # Hodnotíme podle využité délky
                vyuzita_delka = sum(s['delka'] for s in kombinace_s_timto)
                if vyuzita_delka > nejlepsi_hodnota:
                    nejlepsi_hodnota = vyuzita_delka
                    nejlepsi = kombinace_s_timto

        memo[key] = nejlepsi
        return nejlepsi

    # Generujeme různé kombinace
    for _ in range(1000):  # Omezíme počet iterací
        dostupne = {}
        for delka, segmenty in unikatni_delky.items():
            if segmenty:
                # Náhodně vybereme kolik kusů této délky použijeme (max 10)
                max_kusy = min(10, len(segmenty))
                pocet = random.randint(1, max_kusy)
                dostupne[delka] = segmenty[:pocet]

        if dostupne:
            kombinace_result = najdi_nejlepsi_kombinace(TYCA_DELKA, dostupne)
            if kombinace_result:
                kombinace.append(kombinace_result)

    # Přidáme jednoduché kombinace (jeden typ segmentu)
    for delka, segmenty in unikatni_delky.items():
        potrebna_delka = delka + PROREZ_NA_REZ
        max_kusy = int(TYCA_DELKA // potrebna_delka)
        for pocet in range(1, min(max_kusy + 1, len(segmenty) + 1)):
            kombinace.append(segmenty[:pocet])

    # Seřadíme podle efektivity
    def vypocti_efektivitu(komb):
        celkova_delka = sum(seg['delka'] for seg in komb)
        celkovy_prorez = len(komb) * PROREZ_NA_REZ if komb else 0
        return celkova_delka / (TYCA_DELKA - celkovy_prorez) if (TYCA_DELKA - celkovy_prorez) > 0 else 0

    kombinace.sort(key=vypocti_efektivitu, reverse=True)

    # Odebereme duplicity
    unikatni_kombinace = []
    seen = set()
    for komb in kombinace:
        key = tuple(sorted((seg['oznaceni'], seg['delka']) for seg in komb))
        if key not in seen:
            seen.add(key)
            unikatni_kombinace.append(komb)

    return unikatni_kombinace[:5000]  # Omezíme na 5000 nejlepších

def optimalizuj_rezani_ffd(segments):
    """
    First Fit Decreasing algoritmus pro optimalizaci řezání
    """
    print("Používám First Fit Decreasing algoritmus...")

    # Vytvoříme seznam všech segmentů seřazených podle délky (sestupně)
    vsechny_segmenty = []
    for seg in segments:
        for _ in range(seg['ks']):
            vsechny_segmenty.append(seg.copy())

    # Seřadíme podle délky sestupně
    vsechny_segmenty.sort(key=lambda x: x['delka'], reverse=True)

    print(f"Celkem {len(vsechny_segmenty)} segmentů k umístění")

    tyci = []  # Seznam tyčí, každá tyč je seznam segmentů

    for segment in vsechny_segmenty:
        umisten = False
        potrebna_delka = segment['delka'] + PROREZ_NA_REZ

        # Zkusíme umístit segment do existující tyče
        for tyc in tyci:
            # Vypočítáme aktuální využití tyče
            aktualni_delka = sum(s['delka'] for s in tyc)
            aktualni_prorez = len(tyc) * PROREZ_NA_REZ
            zbyvajici_misto = TYCA_DELKA - aktualni_delka - aktualni_prorez

            if potrebna_delka <= zbyvajici_misto:
                tyc.append(segment)
                umisten = True
                break

        # Pokud se segment nevešel do žádné existující tyče, vytvoříme novou
        if not umisten:
            tyci.append([segment])

    return tyci

def optimalizuj_rezani_best_fit(segments):
    """
    Best Fit algoritmus - umisťuje segment do tyče s nejmenším zbývajícím místem
    """
    print("Používám Best Fit algoritmus...")

    # Vytvoříme seznam všech segmentů seřazených podle délky (sestupně)
    vsechny_segmenty = []
    for seg in segments:
        for _ in range(seg['ks']):
            vsechny_segmenty.append(seg.copy())

    # Seřadíme podle délky sestupně
    vsechny_segmenty.sort(key=lambda x: x['delka'], reverse=True)

    print(f"Celkem {len(vsechny_segmenty)} segmentů k umístění")

    tyci = []  # Seznam tyčí, každá tyč je seznam segmentů

    for segment in vsechny_segmenty:
        potrebna_delka = segment['delka'] + PROREZ_NA_REZ

        nejlepsi_tyc = None
        nejmensi_zbytek = float('inf')

        # Najdeme tyč s nejmenším zbývajícím místem, kam se segment vejde
        for i, tyc in enumerate(tyci):
            aktualni_delka = sum(s['delka'] for s in tyc)
            aktualni_prorez = len(tyc) * PROREZ_NA_REZ
            zbyvajici_misto = TYCA_DELKA - aktualni_delka - aktualni_prorez

            if potrebna_delka <= zbyvajici_misto and zbyvajici_misto < nejmensi_zbytek:
                nejmensi_zbytek = zbyvajici_misto
                nejlepsi_tyc = i

        # Umístíme segment
        if nejlepsi_tyc is not None:
            tyci[nejlepsi_tyc].append(segment)
        else:
            # Vytvoříme novou tyč
            tyci.append([segment])

    return tyci

def vytvor_excel_vystup(tyci, segments):
    """Vytvoří Excel soubor s řezacím plánem"""
    
    # Vytvoříme DataFrame pro výstup
    data = []
    celkovy_odpad = 0
    
    # Barvy pro různé segmenty
    barvy = [
        'FFE6E6', 'E6F3FF', 'E6FFE6', 'FFF0E6', 'F0E6FF',
        'FFFFE6', 'E6FFFF', 'FFE6F0', 'F0FFE6', 'E6E6FF',
        'FFEEE6', 'E6FFEE', 'EEE6FF', 'FFEEEE', 'EEFFEE'
    ]
    
    # Mapování segmentů na barvy
    unikatni_segmenty = list(set(seg['oznaceni'] for seg in segments))
    barva_map = {seg: barvy[i % len(barvy)] for i, seg in enumerate(unikatni_segmenty)}
    
    for i, tyc in enumerate(tyci, 1):
        radek = {'Tyč': f'Tyč {i}'}
        
        # Přidáme segmenty
        celkova_delka_tyci = 0
        celkovy_prorez_tyci = 0
        
        for j, segment in enumerate(tyc):
            col_name = f'Segment_{j+1}'
            radek[col_name] = f"{segment['oznaceni']} ({segment['delka']}cm)"
            celkova_delka_tyci += segment['delka']
            celkovy_prorez_tyci += PROREZ_NA_REZ
        
        # Vypočítáme odpad
        odpad = TYCA_DELKA - celkova_delka_tyci - celkovy_prorez_tyci
        radek['Odpad'] = f'{odpad:.1f}cm'
        celkovy_odpad += odpad
        
        data.append(radek)
    
    # Vytvoříme DataFrame
    df = pd.DataFrame(data)
    
    # Přidáme souhrnné řádky
    souhrn_data = []
    
    # Kontrolní součet
    celkova_delka_segmentu = sum(seg['celkem_delka'] for seg in segments)
    pocet_tyci = len(tyci)
    
    souhrn_data.append({
        'Tyč': 'SOUHRN:',
        'Segment_1': f'Celková délka segmentů: {celkova_delka_segmentu}cm',
        'Segment_2': f'Počet tyčí: {pocet_tyci}',
        'Segment_3': f'Celkový odpad: {celkovy_odpad:.1f}cm',
        'Odpad': f'Efektivita: {((celkova_delka_segmentu/(pocet_tyci*TYCA_DELKA))*100):.1f}%'
    })
    
    # Přidáme souhrn do DataFrame
    souhrn_df = pd.DataFrame(souhrn_data)
    df_final = pd.concat([df, souhrn_df], ignore_index=True)
    
    # Uložíme do Excel s formátováním
    with pd.ExcelWriter('rezaci_plan.xlsx', engine='openpyxl') as writer:
        df_final.to_excel(writer, sheet_name='Řezací plán', index=False)
        
        # Získáme workbook a worksheet pro formátování
        workbook = writer.book
        worksheet = writer.sheets['Řezací plán']
        
        # Formátování
        for row_idx, row in enumerate(dataframe_to_rows(df_final, index=False, header=True), 1):
            for col_idx, value in enumerate(row, 1):
                cell = worksheet.cell(row=row_idx, column=col_idx, value=value)
                
                # Formátování záhlaví
                if row_idx == 1:
                    cell.font = Font(bold=True, size=12)
                    cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
                
                # Formátování segmentů podle barev
                elif row_idx <= len(tyci) + 1 and col_idx > 1 and col_idx < len(df_final.columns):
                    if isinstance(value, str) and '(' in str(value):
                        oznaceni = str(value).split('(')[0].strip()
                        if oznaceni in barva_map:
                            cell.fill = PatternFill(start_color=barva_map[oznaceni], 
                                                  end_color=barva_map[oznaceni], fill_type='solid')
                
                # Formátování souhrnu
                elif row_idx > len(tyci) + 1:
                    cell.font = Font(bold=True, size=11)
                    cell.fill = PatternFill(start_color='FFFFCC', end_color='FFFFCC', fill_type='solid')
                
                # Ohraničení
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
        
        # Nastavíme šířku sloupců
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    return df_final, celkovy_odpad, pocet_tyci

def main():
    """Hlavní funkce"""
    print("=== OPTIMALIZACE ŘEZÁNÍ VÝZTUŽE ===")
    print("Načítám data...")
    
    segments = nacti_data()
    
    print(f"Načteno {len(segments)} typů segmentů")
    print(f"Celkem kusů: {sum(seg['ks'] for seg in segments)}")
    print(f"Celková délka: {sum(seg['celkem_delka'] for seg in segments)}cm")
    
    print("\nOptimalizuji řezání...")

    # Zkusíme oba algoritmy a vybereme lepší
    print("Testování FFD algoritmu...")
    tyci_ffd = optimalizuj_rezani_ffd(segments)

    print("Testování Best Fit algoritmu...")
    tyci_bf = optimalizuj_rezani_best_fit(segments)

    # Vybereme lepší výsledek
    def vypocti_efektivitu_tyci(tyci_list):
        celkova_delka = sum(sum(seg['delka'] for seg in tyc) for tyc in tyci_list)
        celkovy_material = len(tyci_list) * TYCA_DELKA
        return celkova_delka / celkovy_material if celkovy_material > 0 else 0

    efektivita_ffd = vypocti_efektivitu_tyci(tyci_ffd)
    efektivita_bf = vypocti_efektivitu_tyci(tyci_bf)

    if efektivita_ffd >= efektivita_bf:
        print(f"Vybrán FFD algoritmus (efektivita: {efektivita_ffd:.1%})")
        tyci = tyci_ffd
    else:
        print(f"Vybrán Best Fit algoritmus (efektivita: {efektivita_bf:.1%})")
        tyci = tyci_bf
    
    print(f"\nVýsledek optimalizace:")
    print(f"Počet potřebných tyčí: {len(tyci)}")
    
    print("\nVytvářím Excel výstup...")
    df, celkovy_odpad, pocet_tyci = vytvor_excel_vystup(tyci, segments)
    
    print(f"\n=== VÝSLEDKY ===")
    print(f"Počet tyčí k nákupu: {pocet_tyci}")
    print(f"Celkový odpad: {celkovy_odpad:.1f}cm")
    print(f"Efektivita využití: {((sum(seg['celkem_delka'] for seg in segments)/(pocet_tyci*TYCA_DELKA))*100):.1f}%")
    print(f"\nVýstup uložen do: rezaci_plan.xlsx")

if __name__ == "__main__":
    main()
