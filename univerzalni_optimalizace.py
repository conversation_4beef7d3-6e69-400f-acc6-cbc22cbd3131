#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Univerzální optimalizace řezání výztuže pro všechny soubory
Podporuje různé průměry a délky tyčí
"""

import pandas as pd
import numpy as np
from itertools import combinations_with_replacement, product
import openpyxl
from openpyxl.styles import PatternFill, Font, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import random
from collections import defaultdict
import os

# Konstanty
STANDARDNI_DELKY_TYCI = {
    6: 600,   # 6mm průměr - 6m tyče
    8: 600,   # 8mm průměr - 6m tyče  
    10: 600,  # 10mm průměr - 6m tyče
    12: 600,  # 12mm průměr - 6m tyče
    14: 600,  # 14mm průměr - 6m tyče
    16: 600   # 16mm průměr - 6m tyče
}

DELSI_TYCI = {
    6: [800, 1000, 1200],   # 8m, 10m, 12m tyče pro 6mm
    8: [800, 1000, 1200],   # 8m, 10m, 12m tyče pro 8mm
    10: [800, 1000, 1200],  # 8m, 10m, 12m tyče pro 10mm
    12: [800, 1000, 1200],  # 8m, 10m, 12m tyče pro 12mm
    14: [800, 1000, 1200],  # 8m, 10m, 12m tyče pro 14mm
    16: [800, 1000, 1200]   # 8m, 10m, 12m tyče pro 16mm
}

PROREZ_NA_REZ = 0.4  # cm (4mm)

def nacti_data_ze_souboru(filename):
    """Načte data z Excel souboru"""
    df = pd.read_excel(filename)
    df_clean = df[['oznaceni', 'prumer', 'delka (m)', 'ks', 'celkem delka (m)']].dropna()
    
    segments = []
    prumer = df_clean['prumer'].iloc[0]
    
    for _, row in df_clean.iterrows():
        delka_cm = int(row['delka (m)'] * 100)  # převod na cm
        segments.append({
            'oznaceni': row['oznaceni'],
            'prumer': prumer,
            'delka': delka_cm,
            'ks': row['ks'],
            'celkem_delka': delka_cm * row['ks']
        })
    
    return segments, prumer

def optimalizuj_s_delsi_tyci(segments, prumer):
    """
    Optimalizuje s možností použití delších tyčí
    Vrací nejlepší kombinaci standardních a delších tyčí
    """
    standardni_delka = STANDARDNI_DELKY_TYCI[prumer]
    delsi_delky = DELSI_TYCI.get(prumer, [])
    
    nejlepsi_reseni = None
    nejlepsi_cena = float('inf')
    
    # Ceny tyčí (orientační poměr)
    ceny_tyci = {
        standardni_delka: 1.0,  # základní cena
    }
    
    for delsi_delka in delsi_delky:
        # Cena delší tyče je úměrná délce + malý příplatek
        ceny_tyci[delsi_delka] = (delsi_delka / standardni_delka) * 1.1
    
    # Zkusíme různé kombinace délek tyčí
    for pouzit_delsi in [False, True]:
        if pouzit_delsi:
            dostupne_delky = [standardni_delka] + delsi_delky
        else:
            dostupne_delky = [standardni_delka]
        
        for tyc_delka in dostupne_delky:
            tyci = optimalizuj_pro_delku_tyci(segments, tyc_delka)
            
            # Vypočítáme celkovou cenu
            celkova_cena = len(tyci) * ceny_tyci[tyc_delka]
            
            if celkova_cena < nejlepsi_cena:
                nejlepsi_cena = celkova_cena
                nejlepsi_reseni = {
                    'tyci': tyci,
                    'delka_tyci': tyc_delka,
                    'pocet_tyci': len(tyci),
                    'cena': celkova_cena,
                    'cena_za_tyc': ceny_tyci[tyc_delka]
                }
    
    return nejlepsi_reseni

def optimalizuj_pro_delku_tyci(segments, delka_tyci):
    """FFD algoritmus pro konkrétní délku tyče"""
    # Vytvoříme seznam všech segmentů seřazených podle délky (sestupně)
    vsechny_segmenty = []
    for seg in segments:
        for _ in range(seg['ks']):
            vsechny_segmenty.append(seg.copy())
    
    # Seřadíme podle délky sestupně
    vsechny_segmenty.sort(key=lambda x: x['delka'], reverse=True)
    
    tyci = []  # Seznam tyčí, každá tyč je seznam segmentů
    
    for segment in vsechny_segmenty:
        umisten = False
        potrebna_delka = segment['delka'] + PROREZ_NA_REZ
        
        # Zkusíme umístit segment do existující tyče
        for tyc in tyci:
            # Vypočítáme aktuální využití tyče
            aktualni_delka = sum(s['delka'] for s in tyc)
            aktualni_prorez = len(tyc) * PROREZ_NA_REZ
            zbyvajici_misto = delka_tyci - aktualni_delka - aktualni_prorez
            
            if potrebna_delka <= zbyvajici_misto:
                tyc.append(segment)
                umisten = True
                break
        
        # Pokud se segment nevešel do žádné existující tyče, vytvoříme novou
        if not umisten:
            tyci.append([segment])
    
    return tyci

def vytvor_excel_vystup_univerzalni(reseni, segments, filename_prefix):
    """Vytvoří Excel soubor s řezacím plánem"""
    
    tyci = reseni['tyci']
    delka_tyci = reseni['delka_tyci']
    prumer = segments[0]['prumer']
    
    # Vytvoříme DataFrame pro výstup
    data = []
    celkovy_odpad = 0
    
    # Barvy pro různé segmenty
    barvy = [
        'FFE6E6', 'E6F3FF', 'E6FFE6', 'FFF0E6', 'F0E6FF',
        'FFFFE6', 'E6FFFF', 'FFE6F0', 'F0FFE6', 'E6E6FF',
        'FFEEE6', 'E6FFEE', 'EEE6FF', 'FFEEEE', 'EEFFEE'
    ]
    
    # Mapování segmentů na barvy
    unikatni_segmenty = list(set(seg['oznaceni'] for seg in segments))
    barva_map = {seg: barvy[i % len(barvy)] for i, seg in enumerate(unikatni_segmenty)}
    
    for i, tyc in enumerate(tyci, 1):
        radek = {'Tyč': f'Tyč {i} ({delka_tyci/100:.0f}m)'}
        
        # Přidáme segmenty
        celkova_delka_tyci = 0
        celkovy_prorez_tyci = 0
        
        for j, segment in enumerate(tyc):
            col_name = f'Segment_{j+1}'
            radek[col_name] = f"{segment['oznaceni']} ({segment['delka']}cm)"
            celkova_delka_tyci += segment['delka']
            celkovy_prorez_tyci += PROREZ_NA_REZ
        
        # Vypočítáme odpad
        odpad = delka_tyci - celkova_delka_tyci - celkovy_prorez_tyci
        radek['Odpad'] = f'{odpad:.1f}cm'
        celkovy_odpad += odpad
        
        data.append(radek)
    
    # Vytvoříme DataFrame
    df = pd.DataFrame(data)
    
    # Přidáme souhrnné řádky
    souhrn_data = []
    
    # Kontrolní součet
    celkova_delka_segmentu = sum(seg['celkem_delka'] for seg in segments)
    pocet_tyci = len(tyci)
    celkovy_material = pocet_tyci * delka_tyci
    efektivita = (celkova_delka_segmentu / celkovy_material) * 100 if celkovy_material > 0 else 0
    
    souhrn_data.append({
        'Tyč': 'SOUHRN:',
        'Segment_1': f'Celková délka segmentů: {celkova_delka_segmentu}cm',
        'Segment_2': f'Počet tyčí: {pocet_tyci} x {delka_tyci/100:.0f}m',
        'Segment_3': f'Celkový odpad: {celkovy_odpad:.1f}cm',
        'Odpad': f'Efektivita: {efektivita:.1f}%'
    })
    
    # Ekonomické vyhodnocení
    if delka_tyci > STANDARDNI_DELKY_TYCI[prumer]:
        souhrn_data.append({
            'Tyč': 'EKONOMIKA:',
            'Segment_1': f'Cena za tyč: {reseni["cena_za_tyc"]:.1f}x standardní',
            'Segment_2': f'Celková cena: {reseni["cena"]:.1f}x standardní',
            'Segment_3': f'Úspora materiálu: {((celkovy_material - celkova_delka_segmentu)/100):.1f}m',
            'Odpad': f'Průměr: {prumer}mm'
        })
    
    # Přidáme souhrn do DataFrame
    souhrn_df = pd.DataFrame(souhrn_data)
    df_final = pd.concat([df, souhrn_df], ignore_index=True)
    
    # Název výstupního souboru
    output_filename = f'rezaci_plan_{filename_prefix}_{prumer}mm.xlsx'
    
    # Uložíme do Excel s formátováním
    with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
        df_final.to_excel(writer, sheet_name='Řezací plán', index=False)
        
        # Získáme workbook a worksheet pro formátování
        workbook = writer.book
        worksheet = writer.sheets['Řezací plán']
        
        # Formátování (stejné jako v původním scriptu)
        for row_idx, row in enumerate(dataframe_to_rows(df_final, index=False, header=True), 1):
            for col_idx, value in enumerate(row, 1):
                cell = worksheet.cell(row=row_idx, column=col_idx, value=value)
                
                # Formátování záhlaví
                if row_idx == 1:
                    cell.font = Font(bold=True, size=12)
                    cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
                
                # Formátování segmentů podle barev
                elif row_idx <= len(tyci) + 1 and col_idx > 1 and col_idx < len(df_final.columns):
                    if isinstance(value, str) and '(' in str(value):
                        oznaceni = str(value).split('(')[0].strip()
                        if oznaceni in barva_map:
                            cell.fill = PatternFill(start_color=barva_map[oznaceni], 
                                                  end_color=barva_map[oznaceni], fill_type='solid')
                
                # Formátování souhrnu
                elif row_idx > len(tyci) + 1:
                    cell.font = Font(bold=True, size=11)
                    cell.fill = PatternFill(start_color='FFFFCC', end_color='FFFFCC', fill_type='solid')
                
                # Ohraničení
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
        
        # Nastavíme šířku sloupců
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    return output_filename, df_final, celkovy_odpad, pocet_tyci, efektivita

def zpracuj_soubor(filename):
    """Zpracuje jeden soubor"""
    print(f"\n=== ZPRACOVÁVÁM SOUBOR {filename} ===")
    
    # Načteme data
    segments, prumer = nacti_data_ze_souboru(filename)
    
    print(f"Průměr výztuže: {prumer}mm")
    print(f"Celkem segmentů: {sum(seg['ks'] for seg in segments)}")
    print(f"Celková délka: {sum(seg['celkem_delka'] for seg in segments)}cm")
    
    # Optimalizujeme
    reseni = optimalizuj_s_delsi_tyci(segments, prumer)
    
    # Vytvoříme Excel výstup
    filename_prefix = filename.replace('.xlsx', '')
    output_file, df, odpad, pocet_tyci, efektivita = vytvor_excel_vystup_univerzalni(
        reseni, segments, filename_prefix)
    
    print(f"\nVÝSLEDKY PRO {filename}:")
    print(f"Nejlepší řešení: {pocet_tyci} tyčí po {reseni['delka_tyci']/100:.0f}m")
    print(f"Efektivita: {efektivita:.1f}%")
    print(f"Celkový odpad: {odpad:.1f}cm")
    print(f"Výstup uložen: {output_file}")
    
    return {
        'soubor': filename,
        'prumer': prumer,
        'pocet_tyci': pocet_tyci,
        'delka_tyci': reseni['delka_tyci'],
        'efektivita': efektivita,
        'odpad': odpad,
        'output_file': output_file
    }

def main():
    """Hlavní funkce"""
    print("=== UNIVERZÁLNÍ OPTIMALIZACE ŘEZÁNÍ VÝZTUŽE ===")

    soubory = ['12.xlsx', '14.xlsx', '16.xlsx']
    vysledky = []
    
    for soubor in soubory:
        if os.path.exists(soubor):
            try:
                vysledek = zpracuj_soubor(soubor)
                vysledky.append(vysledek)
            except Exception as e:
                print(f"Chyba při zpracování {soubor}: {e}")
        else:
            print(f"Soubor {soubor} nenalezen")
    
    # Souhrnný přehled
    print("\n" + "="*60)
    print("SOUHRNNÝ PŘEHLED VŠECH SOUBORŮ")
    print("="*60)
    
    for v in vysledky:
        print(f"{v['soubor']:10} | {v['prumer']:2}mm | {v['pocet_tyci']:3} tyčí | "
              f"{v['delka_tyci']/100:.0f}m | {v['efektivita']:5.1f}% | {v['output_file']}")
    
    print(f"\nCelkem zpracováno: {len(vysledky)} souborů")
    print("Všechny Excel soubory jsou připraveny k tisku!")

if __name__ == "__main__":
    main()
