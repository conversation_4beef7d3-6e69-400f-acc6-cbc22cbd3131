Přidávám excel, kter<PERSON> obsahuje různé délky výztuží a jejich počet. Ty potřebuji nařezat z výztuží, kter<PERSON> se prodávají v délce 6m. Potřebuji vědět kolik koupit 6m kus<PERSON> tak, abych z toho kusy nařezal. Počítej s prořezem 4mm na řez. Ideálně bych i potřeboval schéma, jak za sebe jednotlivé délky řadit, abych měl co nejméně odpadu, případně jej využil na další segmenty (Branch & Bound / Integer Linear Programming nebo Integer Linear Programming (ILP) s Column Generation – to je metoda, kterou používají optimalizátory v ocelárnách a pilách.)
Sloupce: oznaceni - značka segmentu (tu chci mít u každého segmentu, ať vím kam patří), prumer - <PERSON><PERSON> p<PERSON>, delka - na kolik narezat, ks - kolik kusů dan<PERSON>, celkem delka - kontrolní součet daného řádku. 

Výsledný řezací plán chci mít v excelu tak, aby šel vytisknout na A4 barevně a byl čitelný na slunku venku. Každou tyč na jeden řádek a jednotlivé segmenty barevně odlišit a každý segment bude samostatný sloupec. Odpad co zbyde tak jako poslední sloupec. Sloupec odpadu dole na konci sečíst. Dále na konci chci kontrolní součet - celková délka všech segmentů a počet tyčí, které musím koupit. Jako jednotku segmentů použij cm.
