#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Speciální optimalizace pro soubor 14 s podporou delších tyčí
Ukáže porovnání 6m vs 8m vs 10m vs 12m tyčí
"""

import pandas as pd
import numpy as np
import openpyxl
from openpyxl.styles import PatternFill, Font, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from collections import defaultdict

PROREZ_NA_REZ = 0.4  # cm (4mm)

def nacti_data_14():
    """Načte data ze souboru 14.xlsx"""
    df = pd.read_excel('14.xlsx')
    df_clean = df[['oznaceni', 'prumer', 'delka (m)', 'ks', 'celkem delka (m)']].dropna()
    
    segments = []
    prumer = df_clean['prumer'].iloc[0]
    
    for _, row in df_clean.iterrows():
        delka_cm = int(row['delka (m)'] * 100)  # převod na cm
        segments.append({
            'oznaceni': row['oznaceni'],
            'prumer': prumer,
            'delka': delka_cm,
            'ks': row['ks'],
            'celkem_delka': delka_cm * row['ks']
        })
    
    return segments, prumer

def optimalizuj_ffd(segments, delka_tyci):
    """FFD algoritmus pro konkrétní délku tyče"""
    # Vytvoříme seznam všech segmentů seřazených podle délky (sestupně)
    vsechny_segmenty = []
    for seg in segments:
        for _ in range(seg['ks']):
            vsechny_segmenty.append(seg.copy())
    
    # Seřadíme podle délky sestupně
    vsechny_segmenty.sort(key=lambda x: x['delka'], reverse=True)
    
    tyci = []  # Seznam tyčí, každá tyč je seznam segmentů
    
    for segment in vsechny_segmenty:
        umisten = False
        potrebna_delka = segment['delka'] + PROREZ_NA_REZ
        
        # Zkusíme umístit segment do existující tyče
        for tyc in tyci:
            # Vypočítáme aktuální využití tyče
            aktualni_delka = sum(s['delka'] for s in tyc)
            aktualni_prorez = len(tyc) * PROREZ_NA_REZ
            zbyvajici_misto = delka_tyci - aktualni_delka - aktualni_prorez
            
            if potrebna_delka <= zbyvajici_misto:
                tyc.append(segment)
                umisten = True
                break
        
        # Pokud se segment nevešel do žádné existující tyče, vytvoříme novou
        if not umisten:
            tyci.append([segment])
    
    return tyci

def vypocti_napojeni_tyci(delka_pozadovana, delka_standardni):
    """
    Vypočítá kolik standardních tyčí je potřeba pro dosažení požadované délky
    Například: 3x 8m = 2400cm, potřebuji 4x 6m = 2400cm
    """
    celkova_pozadovana = delka_pozadovana
    pocet_standardnich = int(np.ceil(celkova_pozadovana / delka_standardni))
    celkova_standardni = pocet_standardnich * delka_standardni
    
    return {
        'pozadovana_delka': delka_pozadovana,
        'standardni_delka': delka_standardni,
        'pocet_standardnich': pocet_standardnich,
        'celkova_standardni': celkova_standardni,
        'prebytecna_delka': celkova_standardni - celkova_pozadovana
    }

def porovnej_delky_tyci(segments):
    """Porovná efektivitu různých délek tyčí"""
    delky_tyci = [600, 800, 1000, 1200]  # 6m, 8m, 10m, 12m
    vysledky = {}
    
    for delka in delky_tyci:
        tyci = optimalizuj_ffd(segments, delka)
        
        # Vypočítáme statistiky
        celkova_delka_segmentu = sum(seg['celkem_delka'] for seg in segments)
        pocet_tyci = len(tyci)
        celkovy_material = pocet_tyci * delka
        efektivita = (celkova_delka_segmentu / celkovy_material) * 100 if celkovy_material > 0 else 0
        
        # Vypočítáme odpad
        celkovy_odpad = 0
        for tyc in tyci:
            aktualni_delka = sum(s['delka'] for s in tyc)
            aktualni_prorez = len(tyc) * PROREZ_NA_REZ
            odpad = delka - aktualni_delka - aktualni_prorez
            celkovy_odpad += odpad
        
        # Pokud používáme delší tyče, vypočítáme napojení
        napojeni = None
        if delka > 600:
            napojeni = vypocti_napojeni_tyci(pocet_tyci * delka, 600)
        
        vysledky[delka] = {
            'tyci': tyci,
            'pocet_tyci': pocet_tyci,
            'efektivita': efektivita,
            'celkovy_odpad': celkovy_odpad,
            'celkovy_material': celkovy_material,
            'napojeni': napojeni
        }
    
    return vysledky

def vytvor_porovnani_excel(vysledky, segments):
    """Vytvoří Excel s porovnáním všech variant"""
    
    # Vytvoříme souhrnnou tabulku
    souhrn_data = []
    
    for delka, data in vysledky.items():
        radek = {
            'Délka_tyče': f'{delka/100:.0f}m',
            'Počet_tyčí': data['pocet_tyci'],
            'Efektivita': f"{data['efektivita']:.1f}%",
            'Celkový_odpad': f"{data['celkovy_odpad']:.1f}cm",
            'Celkový_materiál': f"{data['celkovy_material']/100:.1f}m"
        }
        
        # Přidáme informace o napojení
        if data['napojeni']:
            nap = data['napojeni']
            radek['Napojení_6m'] = f"{nap['pocet_standardnich']} tyčí"
            radek['Přebytek'] = f"{nap['prebytecna_delka']/100:.1f}m"
            radek['Poměr_ceny'] = f"{nap['pocet_standardnich']/data['pocet_tyci']:.2f}x"
        else:
            radek['Napojení_6m'] = "N/A"
            radek['Přebytek'] = "N/A"
            radek['Poměr_ceny'] = "1.00x"
        
        souhrn_data.append(radek)
    
    # Vytvoříme Excel soubor
    with pd.ExcelWriter('porovnani_delky_tyci_14.xlsx', engine='openpyxl') as writer:
        
        # Sheet 1: Souhrnné porovnání
        df_souhrn = pd.DataFrame(souhrn_data)
        df_souhrn.to_excel(writer, sheet_name='Porovnání', index=False)
        
        # Formátování souhrnného sheetu
        ws_souhrn = writer.sheets['Porovnání']
        for row in ws_souhrn.iter_rows(min_row=1, max_row=1):
            for cell in row:
                cell.font = Font(bold=True, size=12)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
        
        # Sheet 2-5: Detailní plány pro každou délku
        barvy = ['FFE6E6', 'E6F3FF', 'E6FFE6', 'FFF0E6', 'F0E6FF', 'FFFFE6', 'E6FFFF']
        unikatni_segmenty = list(set(seg['oznaceni'] for seg in segments))
        barva_map = {seg: barvy[i % len(barvy)] for i, seg in enumerate(unikatni_segmenty)}
        
        for delka, data in vysledky.items():
            sheet_name = f'{delka/100:.0f}m_tyče'
            
            # Vytvoříme data pro tento sheet
            plan_data = []
            for i, tyc in enumerate(data['tyci'], 1):
                radek = {'Tyč': f'Tyč {i}'}
                
                for j, segment in enumerate(tyc):
                    col_name = f'Segment_{j+1}'
                    radek[col_name] = f"{segment['oznaceni']} ({segment['delka']}cm)"
                
                # Vypočítáme odpad
                aktualni_delka = sum(s['delka'] for s in tyc)
                aktualni_prorez = len(tyc) * PROREZ_NA_REZ
                odpad = delka - aktualni_delka - aktualni_prorez
                radek['Odpad'] = f'{odpad:.1f}cm'
                
                plan_data.append(radek)
            
            # Přidáme souhrn
            celkova_delka_segmentu = sum(seg['celkem_delka'] for seg in segments)
            plan_data.append({
                'Tyč': 'SOUHRN:',
                'Segment_1': f'Délka segmentů: {celkova_delka_segmentu}cm',
                'Segment_2': f'Počet tyčí: {data["pocet_tyci"]}',
                'Segment_3': f'Efektivita: {data["efektivita"]:.1f}%',
                'Odpad': f'Odpad: {data["celkovy_odpad"]:.1f}cm'
            })
            
            # Přidáme napojení pokud je
            if data['napojeni']:
                nap = data['napojeni']
                plan_data.append({
                    'Tyč': 'NAPOJENÍ:',
                    'Segment_1': f'Potřeba {data["pocet_tyci"]}x {delka/100:.0f}m = {data["celkovy_material"]/100:.1f}m',
                    'Segment_2': f'Koupit {nap["pocet_standardnich"]}x 6m = {nap["celkova_standardni"]/100:.1f}m',
                    'Segment_3': f'Přebytek: {nap["prebytecna_delka"]/100:.1f}m',
                    'Odpad': f'Poměr: {nap["pocet_standardnich"]/data["pocet_tyci"]:.2f}x'
                })
            
            df_plan = pd.DataFrame(plan_data)
            df_plan.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # Formátování
            ws = writer.sheets[sheet_name]
            for row_idx, row in enumerate(dataframe_to_rows(df_plan, index=False, header=True), 1):
                for col_idx, value in enumerate(row, 1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    
                    # Záhlaví
                    if row_idx == 1:
                        cell.font = Font(bold=True, size=12)
                        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
                    
                    # Barevné segmenty
                    elif row_idx <= len(data['tyci']) + 1 and col_idx > 1 and 'Segment_' in str(df_plan.columns[col_idx-1]):
                        if isinstance(value, str) and '(' in str(value):
                            oznaceni = str(value).split('(')[0].strip()
                            if oznaceni in barva_map:
                                cell.fill = PatternFill(start_color=barva_map[oznaceni], 
                                                      end_color=barva_map[oznaceni], fill_type='solid')
                    
                    # Souhrn a napojení
                    elif row_idx > len(data['tyci']) + 1:
                        cell.font = Font(bold=True, size=11)
                        if 'NAPOJENÍ' in str(value):
                            cell.fill = PatternFill(start_color='FFE6CC', end_color='FFE6CC', fill_type='solid')
                        else:
                            cell.fill = PatternFill(start_color='FFFFCC', end_color='FFFFCC', fill_type='solid')
                    
                    # Ohraničení
                    cell.border = Border(
                        left=Side(style='thin'), right=Side(style='thin'),
                        top=Side(style='thin'), bottom=Side(style='thin')
                    )
            
            # Šířka sloupců
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

def main():
    """Hlavní funkce"""
    print("=== OPTIMALIZACE SOUBORU 14 S DELŠÍMI TYČEMI ===")
    
    # Načteme data
    segments, prumer = nacti_data_14()
    
    print(f"Průměr výztuže: {prumer}mm")
    print(f"Celkem segmentů: {sum(seg['ks'] for seg in segments)}")
    print(f"Celková délka: {sum(seg['celkem_delka'] for seg in segments)}cm")
    
    # Porovnáme různé délky tyčí
    print("\nPorovnávám různé délky tyčí...")
    vysledky = porovnej_delky_tyci(segments)
    
    # Vytvoříme Excel s porovnáním
    vytvor_porovnani_excel(vysledky, segments)
    
    print("\n" + "="*80)
    print("POROVNÁNÍ RŮZNÝCH DÉLEK TYČÍ PRO SOUBOR 14")
    print("="*80)
    print(f"{'Délka':>8} | {'Počet':>6} | {'Efekt.':>7} | {'Odpad':>10} | {'Napojení 6m':>12} | {'Poměr':>6}")
    print("-" * 80)
    
    for delka, data in vysledky.items():
        napojeni_text = "N/A"
        pomer_text = "1.00x"
        
        if data['napojeni']:
            nap = data['napojeni']
            napojeni_text = f"{nap['pocet_standardnich']} tyčí"
            pomer_text = f"{nap['pocet_standardnich']/data['pocet_tyci']:.2f}x"
        
        print(f"{delka/100:>6.0f}m | {data['pocet_tyci']:>6} | {data['efektivita']:>6.1f}% | "
              f"{data['celkovy_odpad']:>8.1f}cm | {napojeni_text:>12} | {pomer_text:>6}")
    
    print("\nVýstup uložen do: porovnani_delky_tyci_14.xlsx")
    print("\nDoporučení:")
    
    # Najdeme nejlepší variantu
    nejlepsi_efektivita = max(vysledky.values(), key=lambda x: x['efektivita'])
    nejlepsi_material = min(vysledky.values(), key=lambda x: x['celkovy_material'])
    
    for delka, data in vysledky.items():
        if data == nejlepsi_efektivita:
            print(f"- Nejvyšší efektivita: {delka/100:.0f}m tyče ({data['efektivita']:.1f}%)")
        if data == nejlepsi_material:
            print(f"- Nejméně materiálu: {delka/100:.0f}m tyče ({data['celkovy_material']/100:.1f}m)")

if __name__ == "__main__":
    main()
